package com.example.onelinediary

import androidx.compose.foundation.BorderStroke
import androidx.compose.foundation.border
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.automirrored.filled.ArrowBack
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.unit.dp
import androidx.compose.ui.Alignment
import kotlinx.coroutines.delay
import com.example.onelinediary.components.standardOutlinedButtonColors
import com.example.onelinediary.components.standardButtonModifier
import com.example.onelinediary.components.standardButtonPadding
import com.example.onelinediary.components.standardButtonShape
import com.example.onelinediary.components.backButtonModifier
import com.example.onelinediary.ui.theme.ButtonGreen

@Composable
fun DailySentenceScreen(
    onSaveToPhone: (String) -> Unit,
    onNavigateBack: () -> Unit = {}
) {
    var text by remember { mutableStateOf("") }
    var showSuccessScreen by remember { mutableStateOf(false) }
    var showSaveDialog by remember { mutableStateOf(false) }

    // Save dialog
    if (showSaveDialog) {
        AlertDialog(
            onDismissRequest = { showSaveDialog = false },
            title = { Text("Do you want to save?") },
            text = { Text("Your text hasn't been saved yet.") },
            confirmButton = {
                TextButton(
                    onClick = {
                        if (text.isNotEmpty()) {
                            onSaveToPhone(text)
                        }
                        showSaveDialog = false
                        onNavigateBack()
                    }
                ) {
                    Text("Yes")
                }
            },
            dismissButton = {
                TextButton(
                    onClick = {
                        showSaveDialog = false
                        onNavigateBack()
                    }
                ) {
                    Text("No")
                }
            }
        )
    }

    // Success popup dialog
    if (showSuccessScreen) {
        SuccessPopupDialog(
            onDismiss = { showSuccessScreen = false }
        )

        // Automatically hide success popup after a delay
        LaunchedEffect(showSuccessScreen) {
            delay(1500)
            showSuccessScreen = false
        }
    }

    Column(
            modifier = Modifier
                .padding(16.dp)
                .fillMaxWidth(),
            verticalArrangement = Arrangement.Center
        ) {
            // Back button at the top
            OutlinedButton(
                onClick = {
                    if (text.isNotEmpty()) {
                        showSaveDialog = true
                    } else {
                        onNavigateBack()
                    }
                },
                modifier = backButtonModifier(Modifier.align(Alignment.Start)),
                contentPadding = standardButtonPadding,
                colors = standardOutlinedButtonColors(),
                shape = standardButtonShape
            ) {
                Icon(
                    imageVector = Icons.AutoMirrored.Filled.ArrowBack,
                    contentDescription = "Back"
                )
            }

            Spacer(modifier = Modifier.height(16.dp))

            Text(
                text = "OneLineDiary",
                style = MaterialTheme.typography.headlineMedium
            )
            Spacer(modifier = Modifier.height(8.dp))
            Text(
                text = "Jouw leven in één zin per dag.",
                style = MaterialTheme.typography.bodyMedium
            )
            Spacer(modifier = Modifier.height(16.dp))
            OutlinedTextField(
                value = text,
                onValueChange = { if (it.length <= 150) text = it },
                label = { Text("Jouw zin van vandaag") },
                singleLine = false,
                maxLines = 3,
                modifier = Modifier.fillMaxWidth()
            )

            if (text.length == 150) {
                Text(
                    text = "maximum allowed is 150 characters",
                    style = MaterialTheme.typography.bodySmall,
                    color = MaterialTheme.colorScheme.error
                )
                Spacer(modifier = Modifier.height(16.dp))
            }

            // Row for buttons
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                // Wipe button
                OutlinedButton(
                    onClick = { text = "" },
                    modifier = standardButtonModifier(
                        Modifier.weight(1f),
                        isDisabled = !text.isNotEmpty()
                    ),
                    contentPadding = standardButtonPadding,
                    colors = standardOutlinedButtonColors(),
                    shape = standardButtonShape,
                    enabled = text.isNotEmpty()
                ) {
                    Text("Wipe")
                }

                // Save button
                OutlinedButton(
                    onClick = {
                        if (text.isNotEmpty()) {
                            onSaveToPhone(text)
                            showSuccessScreen = true
                        }
                    },
                    modifier = standardButtonModifier(
                        Modifier.weight(1f),
                        isDisabled = !text.isNotEmpty()
                    ),
                    contentPadding = standardButtonPadding,
                    colors = standardOutlinedButtonColors(),
                    shape = standardButtonShape,
                    enabled = text.isNotEmpty()
                ) {
                    Text("Opslaan")
                }
            }
        }
}

